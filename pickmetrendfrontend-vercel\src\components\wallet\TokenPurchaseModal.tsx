import React, { useState, useEffect } from 'react';
import { tokenPurchaseService, TokenPack } from '../../services/tokenPurchaseService';



interface TokenPurchaseModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (tokensAdded: number) => void;
  currentBalance: number;
}

declare global {
  interface Window {
    Razorpay: any;
  }
}

const TokenPurchaseModal: React.FC<TokenPurchaseModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  currentBalance
}) => {
  const [tokenPacks, setTokenPacks] = useState<TokenPack[]>([]);
  const [selectedPack, setSelectedPack] = useState<TokenPack | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [step, setStep] = useState<'select' | 'payment' | 'processing'>('select');

  // Load token packs when modal opens
  useEffect(() => {
    if (isOpen) {
      loadTokenPacks();
    }
  }, [isOpen]);

  const loadTokenPacks = async () => {
    try {
      setLoading(true);
      const response = await tokenPurchaseService.getTokenPacks();

      if (response.success && response.token_packs) {
        setTokenPacks(response.token_packs);
      } else {
        setError(response.error || 'Failed to load token packs');
      }
    } catch (err) {
      console.error('Error loading token packs:', err);
      setError('Failed to load token packs');
    } finally {
      setLoading(false);
    }
  };

  const handlePackSelect = (pack: TokenPack) => {
    setSelectedPack(pack);
    setStep('payment');
  };

  const handlePurchase = async () => {
    if (!selectedPack) return;

    try {
      setStep('processing');
      setError(null);

      // Create Razorpay order
      const orderResponse = await tokenPurchaseService.createOrder(selectedPack.id);

      if (!orderResponse.success) {
        throw new Error(orderResponse.error || 'Failed to create order');
      }

      const { purchase_id, razorpay_order, token_pack } = orderResponse;

      // Load Razorpay script if not already loaded
      if (!window.Razorpay) {
        await loadRazorpayScript();
      }

      // Configure Razorpay options
      const options = {
        key: razorpay_order.key_id,
        amount: razorpay_order.amount,
        currency: razorpay_order.currency,
        name: 'PickMeTrend',
        description: `Purchase ${token_pack.tokens} tokens`,
        order_id: razorpay_order.order_id,
        prefill: {
          name: 'User',
          email: '<EMAIL>'
        },
        theme: {
          color: '#3399cc'
        },
        handler: async (response: any) => {
          await verifyPayment(purchase_id, response);
        },
        modal: {
          ondismiss: () => {
            setStep('select');
            setError('Payment cancelled');
          }
        }
      };

      // Open Razorpay
      const razorpay = new window.Razorpay(options);
      razorpay.open();

    } catch (err: any) {
      console.error('Error initiating purchase:', err);
      setError(err.message || 'Failed to initiate purchase');
      setStep('payment');
    }
  };

  const verifyPayment = async (purchaseId: string, paymentResponse: any) => {
    try {
      const verifyResponse = await tokenPurchaseService.verifyPayment(
        purchaseId,
        paymentResponse.razorpay_payment_id,
        paymentResponse.razorpay_order_id,
        paymentResponse.razorpay_signature
      );

      if (verifyResponse.success && verifyResponse.tokens_purchased) {
        onSuccess(verifyResponse.tokens_purchased);
        onClose();
        setStep('select');
        setSelectedPack(null);
      } else {
        throw new Error(verifyResponse.error || 'Payment verification failed');
      }
    } catch (err: any) {
      console.error('Error verifying payment:', err);
      setError(err.message || 'Payment verification failed');
      setStep('payment');
    }
  };

  const loadRazorpayScript = (): Promise<void> => {
    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = 'https://checkout.razorpay.com/v1/checkout.js';
      script.onload = () => resolve();
      script.onerror = () => reject(new Error('Failed to load Razorpay script'));
      document.body.appendChild(script);
    });
  };

  const handleBack = () => {
    setStep('select');
    setSelectedPack(null);
    setError(null);
  };

  const handleClose = () => {
    setStep('select');
    setSelectedPack(null);
    setError(null);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="bg-gradient-to-r from-emerald-600 to-teal-600 text-white p-6 rounded-t-2xl">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <span className="text-3xl mr-3">💰</span>
              <div>
                <h2 className="text-2xl font-bold">Buy Tokens</h2>
                <p className="text-emerald-100">Current Balance: {currentBalance} tokens</p>
              </div>
            </div>
            <button
              onClick={handleClose}
              className="text-white hover:text-emerald-200 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {error && (
            <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-center text-red-700">
                <span className="mr-2">⚠️</span>
                <span>{error}</span>
              </div>
            </div>
          )}

          {step === 'select' && (
            <div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Choose a Token Pack</h3>
              
              {loading ? (
                <div className="flex justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-emerald-600"></div>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {tokenPacks.map((pack) => (
                    <div
                      key={pack.id}
                      onClick={() => handlePackSelect(pack)}
                      className={`relative cursor-pointer border-2 rounded-xl p-6 transition-all duration-200 hover:scale-105 ${
                        pack.is_popular
                          ? 'border-emerald-500 bg-emerald-50'
                          : 'border-gray-200 bg-white hover:border-emerald-300'
                      }`}
                    >
                      {pack.is_popular && (
                        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                          <span className="bg-emerald-500 text-white px-3 py-1 rounded-full text-xs font-semibold">
                            POPULAR
                          </span>
                        </div>
                      )}
                      
                      {pack.savings_text && (
                        <div className="absolute -top-3 right-4">
                          <span className="bg-orange-500 text-white px-2 py-1 rounded-full text-xs font-semibold">
                            {pack.savings_text}
                          </span>
                        </div>
                      )}

                      <div className="text-center">
                        <div className="text-3xl mb-2">🪙</div>
                        <h4 className="font-bold text-lg text-gray-900 mb-2">{pack.name}</h4>
                        <div className="text-2xl font-bold text-emerald-600 mb-1">
                          {pack.tokens} tokens
                        </div>
                        <div className="text-xl font-semibold text-gray-900 mb-2">
                          ₹{pack.price_inr}
                        </div>
                        <div className="text-sm text-gray-600">
                          {pack.tokens_per_rupee.toFixed(1)} tokens per ₹
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {step === 'payment' && selectedPack && (
            <div>
              <div className="flex items-center mb-6">
                <button
                  onClick={handleBack}
                  className="mr-4 text-gray-600 hover:text-gray-800"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                </button>
                <h3 className="text-xl font-semibold text-gray-900">Confirm Purchase</h3>
              </div>

              <div className="bg-gray-50 rounded-xl p-6 mb-6">
                <div className="flex items-center justify-between mb-4">
                  <span className="text-gray-600">Token Pack:</span>
                  <span className="font-semibold">{selectedPack.name}</span>
                </div>
                <div className="flex items-center justify-between mb-4">
                  <span className="text-gray-600">Tokens:</span>
                  <span className="font-semibold">{selectedPack.tokens} tokens</span>
                </div>
                <div className="flex items-center justify-between mb-4">
                  <span className="text-gray-600">Price:</span>
                  <span className="font-semibold">₹{selectedPack.price_inr}</span>
                </div>
                <div className="border-t pt-4">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">New Balance:</span>
                    <span className="font-bold text-emerald-600">
                      {currentBalance + selectedPack.tokens} tokens
                    </span>
                  </div>
                </div>
              </div>

              <button
                onClick={handlePurchase}
                className="w-full bg-gradient-to-r from-emerald-600 to-teal-600 text-white font-semibold py-3 px-6 rounded-xl hover:from-emerald-700 hover:to-teal-700 transition-all duration-200"
              >
                Pay ₹{selectedPack.price_inr} with Razorpay
              </button>
            </div>
          )}

          {step === 'processing' && (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-600 mx-auto mb-4"></div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Processing Payment</h3>
              <p className="text-gray-600">Please wait while we process your payment...</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TokenPurchaseModal;
