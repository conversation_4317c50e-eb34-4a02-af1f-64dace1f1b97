import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import axios from 'axios';

interface ExtendedUser {
  id: number;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  is_staff: boolean;
  profile: {
    user_type: string;
    phone_number: string;
    address: string;
    city: string;
    state: string;
    country: string;
    zip_code: string;
  };
}

const ProfileEdit: React.FC = () => {
  const { user, updateUser } = useAuth();
  const navigate = useNavigate();

  const [formData, setFormData] = useState({
    first_name: '',
    last_name: '',
    email: '',
    user_type: 'customer',
    phone_number: '',
    address: '',
    city: '',
    state: '',
    country: '',
    zip_code: ''
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  useEffect(() => {
    if (user) {
      // Cast user to ExtendedUser to use the additional properties
      const extendedUser = user as unknown as ExtendedUser;

      setFormData({
        first_name: extendedUser.first_name || '',
        last_name: extendedUser.last_name || '',
        email: extendedUser.email || '',
        user_type: extendedUser.profile?.user_type || 'customer',
        phone_number: extendedUser.profile?.phone_number || '',
        address: extendedUser.profile?.address || '',
        city: extendedUser.profile?.city || '',
        state: extendedUser.profile?.state || '',
        country: extendedUser.profile?.country || '',
        zip_code: extendedUser.profile?.zip_code || ''
      });
    }
  }, [user]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-e8443.up.railway.app';
      const token = localStorage.getItem('access_token');

      // Try a different approach - send only the fields that are actually used
      const profileData = {
        username: user?.username, // Include username to ensure we're updating the right user
        first_name: formData.first_name,
        last_name: formData.last_name,
        email: formData.email,
        profile: {
          user_type: formData.user_type,
          phone_number: formData.phone_number || null,
          address: formData.address || null,
          city: formData.city || null,
          state: formData.state || null,
          country: formData.country || null,
          zip_code: formData.zip_code || null
        }
      };

      // Log the data being sent to the server
      console.log('Sending profile data:', JSON.stringify(profileData, null, 2));

      // Try using PATCH instead of PUT
      const response = await axios({
        method: 'patch', // Use PATCH instead of PUT
        url: `${baseUrl}/api/accounts/profile/`,
        data: profileData,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `JWT ${token}`
        }
      });

      if (response.status === 200) {
        setSuccess('Profile updated successfully!');

        // Update the user context with the new data
        if (updateUser) {
          updateUser(response.data);
        }

        // Redirect to dashboard after a short delay
        setTimeout(() => {
          navigate('/dashboard');
        }, 2000);
      }
    } catch (err: any) {
      console.error('Error updating profile:', err);

      // Try to extract more detailed error information
      if (err.response?.data) {
        console.error('Response data:', err.response.data);

        // Check if the error is a string message
        if (typeof err.response.data === 'string') {
          setError(err.response.data);
        }
        // Check if it's an object with error messages
        else if (typeof err.response.data === 'object') {
          const errorMessages = [];

          // Loop through all properties in the error object
          for (const key in err.response.data) {
            const value = err.response.data[key];

            // Handle array of error messages
            if (Array.isArray(value)) {
              errorMessages.push(`${key}: ${value.join(', ')}`);
            }
            // Handle nested objects (like profile errors)
            else if (typeof value === 'object' && value !== null) {
              for (const nestedKey in value) {
                const nestedValue = value[nestedKey];
                if (Array.isArray(nestedValue)) {
                  errorMessages.push(`${key}.${nestedKey}: ${nestedValue.join(', ')}`);
                } else {
                  errorMessages.push(`${key}.${nestedKey}: ${nestedValue}`);
                }
              }
            }
            // Handle simple string error
            else {
              errorMessages.push(`${key}: ${value}`);
            }
          }

          if (errorMessages.length > 0) {
            setError(errorMessages.join('\n'));
          } else {
            setError('Failed to update profile. Please try again.');
          }
        } else {
          setError('Failed to update profile. Please try again.');
        }
      } else {
        setError('Failed to update profile. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  if (!user) {
    return (
      <div className="container mx-auto p-4 text-center">
        <p className="text-xl mb-4">Please log in to edit your profile</p>
        <button
          onClick={() => navigate('/login')}
          className="px-6 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          Login
        </button>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 max-w-2xl">
      <h1 className="text-3xl font-bold mb-6">Edit Profile</h1>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {success && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
          {success}
        </div>
      )}

      <div className="bg-white rounded-lg shadow-md p-6">
        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">First Name</label>
              <input
                type="text"
                name="first_name"
                value={formData.first_name}
                onChange={handleChange}
                className="w-full p-2 border rounded"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Last Name</label>
              <input
                type="text"
                name="last_name"
                value={formData.last_name}
                onChange={handleChange}
                className="w-full p-2 border rounded"
              />
            </div>
          </div>

          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
            <input
              type="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              className="w-full p-2 border rounded"
            />
          </div>

          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">Phone Number</label>
            <input
              type="tel"
              name="phone_number"
              value={formData.phone_number}
              onChange={handleChange}
              className="w-full p-2 border rounded"
            />
          </div>

          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">Address</label>
            <input
              type="text"
              name="address"
              value={formData.address}
              onChange={handleChange}
              className="w-full p-2 border rounded"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">City</label>
              <input
                type="text"
                name="city"
                value={formData.city}
                onChange={handleChange}
                className="w-full p-2 border rounded"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">State</label>
              <input
                type="text"
                name="state"
                value={formData.state}
                onChange={handleChange}
                className="w-full p-2 border rounded"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">ZIP Code</label>
              <input
                type="text"
                name="zip_code"
                value={formData.zip_code}
                onChange={handleChange}
                className="w-full p-2 border rounded"
              />
            </div>
          </div>

          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">Country</label>
            <input
              type="text"
              name="country"
              value={formData.country}
              onChange={handleChange}
              className="w-full p-2 border rounded"
            />
          </div>

          <div className="flex justify-between mt-6">
            <button
              type="button"
              onClick={() => navigate('/dashboard')}
              className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              disabled={loading}
            >
              {loading ? 'Saving...' : 'Save Changes'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ProfileEdit;
