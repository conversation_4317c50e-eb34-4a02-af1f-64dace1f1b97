from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
from django.conf import settings
from datetime import timedelta
import uuid
import json

# Spin Wheel models will be defined below


class GameType(models.Model):
    """
    Different types of games available
    """
    name = models.CharField(max_length=50, unique=True)
    display_name = models.CharField(max_length=100)
    description = models.TextField()
    rules = models.JSONField(default=dict, help_text="Game rules and configuration")
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.display_name


class Battle(models.Model):
    """
    1v1 Battle between two players or player vs AI
    """
    BATTLE_STATUS = [
        ('waiting', 'Waiting for Opponent'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
        ('timeout', 'Timeout'),
    ]

    BATTLE_RESULT = [
        ('player1_win', 'Player 1 Wins'),
        ('player2_win', 'Player 2 Wins'),
        ('draw', 'Draw'),
        ('cancelled', 'Cancelled'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    game_type = models.ForeignKey(GameType, on_delete=models.CASCADE)
    
    # Players
    player1 = models.ForeignKey(User, on_delete=models.CASCADE, related_name='battles_as_player1')
    player2 = models.ForeignKey(User, on_delete=models.CASCADE, related_name='battles_as_player2', null=True, blank=True)
    is_ai_battle = models.BooleanField(default=False, help_text="True if player2 is AI")
    
    # Battle state
    status = models.CharField(max_length=20, choices=BATTLE_STATUS, default='waiting')
    result = models.CharField(max_length=20, choices=BATTLE_RESULT, null=True, blank=True)
    
    # Game data
    game_state = models.JSONField(default=dict, help_text="Current game state")
    moves_history = models.JSONField(default=list, help_text="History of all moves")
    
    # Timing
    created_at = models.DateTimeField(auto_now_add=True)
    started_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    
    # Rewards
    tokens_awarded = models.PositiveIntegerField(default=0)
    winner_tokens = models.PositiveIntegerField(default=0)
    loser_tokens = models.PositiveIntegerField(default=0)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        player2_name = self.player2.username if self.player2 else "AI Bot"
        return f"{self.player1.username} vs {player2_name} - {self.game_type.display_name}"

    def start_battle(self):
        """Start the battle"""
        self.status = 'in_progress'
        self.started_at = timezone.now()
        self.save()

    def complete_battle(self, result, winner=None):
        """Complete the battle and award tokens"""
        from django.conf import settings
        from wallet.models import Wallet
        
        self.status = 'completed'
        self.result = result
        self.completed_at = timezone.now()
        
        # Award tokens based on result
        gaming_settings = settings.GAMING_SETTINGS
        win_tokens = gaming_settings.get('TOKEN_REWARD_WIN', 100)
        draw_tokens = gaming_settings.get('TOKEN_REWARD_DRAW', 50)
        participation_tokens = gaming_settings.get('TOKEN_REWARD_PARTICIPATION', 10)
        
        # Get or create wallets
        wallet1, _ = Wallet.objects.get_or_create(user=self.player1)
        if self.player2 and not self.is_ai_battle:
            wallet2, _ = Wallet.objects.get_or_create(user=self.player2)
        
        if result == 'player1_win':
            # Player 1 wins
            wallet1.add_tokens(win_tokens, 'game_win', f'Won battle {self.id}')
            self.winner_tokens = win_tokens
            
            if self.player2 and not self.is_ai_battle:
                wallet2.add_tokens(participation_tokens, 'game_participation', f'Participated in battle {self.id}')
                self.loser_tokens = participation_tokens
                
        elif result == 'player2_win':
            # Player 2 wins (or AI wins)
            if self.player2 and not self.is_ai_battle:
                # Human vs Human: award tokens to winner
                wallet2.add_tokens(win_tokens, 'game_win', f'Won battle {self.id}')
                self.winner_tokens = win_tokens

            # Always award participation tokens to player1 (human player)
            wallet1.add_tokens(participation_tokens, 'game_participation', f'Participated in battle {self.id}')
            self.loser_tokens = participation_tokens
            
        elif result == 'draw':
            # Draw - both get draw tokens
            wallet1.add_tokens(draw_tokens, 'game_draw', f'Draw in battle {self.id}')
            if self.player2 and not self.is_ai_battle:
                wallet2.add_tokens(draw_tokens, 'game_draw', f'Draw in battle {self.id}')
            self.winner_tokens = draw_tokens
            self.loser_tokens = draw_tokens if not self.is_ai_battle else 0
        
        self.tokens_awarded = self.winner_tokens + self.loser_tokens
        self.save()

    @property
    def duration(self):
        """Get battle duration"""
        if self.started_at and self.completed_at:
            return self.completed_at - self.started_at
        return None


class GameMove(models.Model):
    """
    Individual moves in a battle
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    battle = models.ForeignKey(Battle, on_delete=models.CASCADE, related_name='moves')
    player = models.ForeignKey(User, on_delete=models.CASCADE)
    move_data = models.JSONField(help_text="Move data specific to game type")
    move_number = models.PositiveIntegerField()
    timestamp = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['move_number']
        unique_together = ['battle', 'move_number']

    def __str__(self):
        return f"Move {self.move_number} by {self.player.username} in {self.battle.id}"


class PlayerStats(models.Model):
    """
    Player gaming statistics
    """
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='gaming_stats')
    
    # Overall stats
    total_battles = models.PositiveIntegerField(default=0)
    battles_won = models.PositiveIntegerField(default=0)
    battles_lost = models.PositiveIntegerField(default=0)
    battles_drawn = models.PositiveIntegerField(default=0)
    
    # Tokens
    total_tokens_earned = models.PositiveIntegerField(default=0)
    
    # Streaks
    current_win_streak = models.PositiveIntegerField(default=0)
    best_win_streak = models.PositiveIntegerField(default=0)
    
    # Timing
    total_play_time = models.DurationField(default=timezone.timedelta)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.username}'s Gaming Stats"

    @property
    def win_rate(self):
        """Calculate win rate percentage"""
        if self.total_battles == 0:
            return 0
        return round((self.battles_won / self.total_battles) * 100, 2)

    def update_stats(self, battle_result, tokens_earned=0, battle_duration=None):
        """Update stats after a battle"""
        self.total_battles += 1
        self.total_tokens_earned += tokens_earned
        
        if battle_duration:
            self.total_play_time += battle_duration
        
        if battle_result == 'win':
            self.battles_won += 1
            self.current_win_streak += 1
            if self.current_win_streak > self.best_win_streak:
                self.best_win_streak = self.current_win_streak
        elif battle_result == 'loss':
            self.battles_lost += 1
            self.current_win_streak = 0
        elif battle_result == 'draw':
            self.battles_drawn += 1
            # Win streak continues on draw
        
        self.save()


# Signal to create player stats when user is created
from django.db.models.signals import post_save
from django.dispatch import receiver

@receiver(post_save, sender=User)
def create_player_stats(sender, instance, created, **kwargs):
    if created:
        PlayerStats.objects.create(user=instance)

@receiver(post_save, sender=User)
def save_player_stats(sender, instance, **kwargs):
    if hasattr(instance, 'gaming_stats'):
        instance.gaming_stats.save()


class GameSession(models.Model):
    """Track individual game sessions with comprehensive token management"""

    GAME_TYPES = [
        ('tic_tac_toe', 'Tic Tac Toe'),
        ('color_match', 'Color Match'),
        ('memory_card', 'Memory Card Match'),
        ('number_guessing', 'Number Guessing'),
        ('rock_paper_scissors', 'Rock Paper Scissors'),
    ]

    SESSION_STATUS = [
        ('active', 'Active'),
        ('completed', 'Completed'),
        ('incomplete', 'Incomplete/Forfeited'),
        ('pending_replay', 'Pending Replay (Draw)'),
    ]

    GAME_RESULTS = [
        ('win', 'Win'),
        ('loss', 'Loss'),
        ('draw', 'Draw'),
        ('forfeit', 'Forfeit'),
    ]

    # Core fields
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='game_sessions')
    game_type = models.CharField(max_length=20, choices=GAME_TYPES)

    # Session tracking
    status = models.CharField(max_length=20, choices=SESSION_STATUS, default='active')
    result = models.CharField(max_length=10, choices=GAME_RESULTS, null=True, blank=True)

    # Token management
    participation_tokens_deducted = models.IntegerField(default=2)
    tokens_awarded = models.IntegerField(default=0)
    net_token_change = models.IntegerField(default=-2)  # Start with -2 for participation

    # Game data
    game_data = models.JSONField(default=dict, blank=True)  # Store game-specific data
    moves_count = models.IntegerField(default=0)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    started_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    last_activity = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'game_type']),
            models.Index(fields=['status']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"{self.user.username} - {self.get_game_type_display()} - {self.get_status_display()}"

    def start_session(self):
        """Mark session as started"""
        self.started_at = timezone.now()
        self.status = 'active'
        self.save()

    def complete_session(self, result, additional_data=None):
        """Complete the session with result and token calculation"""
        from wallet.models import Wallet

        self.result = result
        self.completed_at = timezone.now()

        # Calculate tokens based on result
        if result == 'win':
            self.tokens_awarded = 5
            self.net_token_change = 3  # 5 - 2 (participation)
            self.status = 'completed'
        elif result == 'loss':
            self.tokens_awarded = -1
            self.net_token_change = -3  # -1 - 2 (participation)
            self.status = 'completed'
        elif result == 'draw':
            self.tokens_awarded = 2
            self.net_token_change = 0  # 2 tokens awarded - 2 participation = 0 net
            self.status = 'completed'  # Draw is completed, not pending
        elif result == 'forfeit':
            self.tokens_awarded = 0
            self.net_token_change = -2  # Only participation fee lost
            self.status = 'incomplete'

        # Update game data if provided
        if additional_data:
            self.game_data.update(additional_data)

        self.save()

        # Update user's wallet based on result
        try:
            wallet = Wallet.objects.get(user=self.user)
            if result == 'win':
                wallet.add_tokens(
                    amount=5,
                    transaction_type='game_win',
                    description=f"Won {self.get_game_type_display()} game"
                )
            elif result == 'draw':
                wallet.add_tokens(
                    amount=2,
                    transaction_type='game_draw',
                    description=f"Draw in {self.get_game_type_display()} game"
                )
            elif result == 'loss':
                if wallet.balance >= 1:
                    wallet.spend_tokens(
                        amount=1,
                        transaction_type='game_loss',
                        description=f"Lost {self.get_game_type_display()} game"
                    )
            # Forfeit doesn't change tokens beyond participation fee
        except Wallet.DoesNotExist:
            pass

        # Update PlayerStats for all game results (including draws)
        self._update_player_stats(result)

        return self.net_token_change

    def _update_player_stats(self, result):
        """Update player statistics after game completion"""
        try:
            # Get or create player stats
            stats, created = PlayerStats.objects.get_or_create(user=self.user)

            # Calculate tokens earned for stats (net change)
            tokens_earned = self.net_token_change if self.net_token_change > 0 else 0

            # Calculate game duration
            battle_duration = None
            if self.started_at and self.completed_at:
                battle_duration = self.completed_at - self.started_at

            # Update stats based on result
            stats.update_stats(result, tokens_earned, battle_duration)

        except Exception as e:
            # Log error but don't fail the game completion
            print(f"Error updating player stats: {e}")

    def forfeit_session(self):
        """Mark session as forfeited"""
        return self.complete_session('forfeit')

    def can_resume(self):
        """Check if session can be resumed"""
        return self.status in ['active', 'pending_replay']

    def is_draw_replay(self):
        """Check if this is a draw that needs replay"""
        return self.status == 'pending_replay' and self.result == 'draw'

    @classmethod
    def cleanup_abandoned_sessions(cls):
        """
        Clean up abandoned game sessions older than 30 minutes
        """
        timeout = timezone.now() - timezone.timedelta(minutes=30)
        abandoned_sessions = cls.objects.filter(
            status='active',
            last_activity__lt=timeout
        )
        
        for session in abandoned_sessions:
            session.status = 'incomplete'
            session.result = 'forfeit'
            session.completed_at = timezone.now()
            session.save()
            
            # Update player stats
            session._update_player_stats('forfeit')
    
    def validate_token_changes(self):
        """
        Validate token changes for the session
        
        Raises:
            ValueError if token changes are invalid
        """
        # Validate participation fee
        if self.participation_tokens_deducted < 0:
            raise ValueError("Participation tokens deducted cannot be negative")
            
        if self.participation_tokens_deducted > 5:  # Max 5 tokens for participation
            raise ValueError("Participation tokens deducted exceeds maximum limit")
            
        # Validate tokens awarded
        if self.tokens_awarded < 0:
            raise ValueError("Tokens awarded cannot be negative")
            
        max_reward = getattr(settings, 'MAX_TOKEN_REWARD', 1000)  # Default max 1000
        if self.tokens_awarded > max_reward:
            raise ValueError(f"Tokens awarded exceeds maximum limit of {max_reward}")
            
        # Validate net token change
        expected_net = self.tokens_awarded - self.participation_tokens_deducted
        if self.net_token_change != expected_net:
            raise ValueError(f"Invalid net token change. Expected {expected_net}, got {self.net_token_change}")
    
    def save(self, *args, **kwargs):
        """Override save to validate token changes"""
        self.validate_token_changes()
        super().save(*args, **kwargs)


# ============================================================================
# SPIN WHEEL MODELS
# ============================================================================

class SpinWheelReward(models.Model):
    """
    Define available rewards for the spin wheel
    """
    REWARD_TYPES = [
        ('tokens', 'Tokens'),
        ('scratch_card', 'Scratch Card'),
        ('discount', 'Discount'),
        ('bonus_spin', 'Bonus Spin'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100, help_text="Display name for the reward")
    reward_type = models.CharField(max_length=20, choices=REWARD_TYPES)
    value = models.PositiveIntegerField(help_text="Token amount, discount percentage, etc.")
    probability = models.FloatField(help_text="Probability of getting this reward (0.0 to 1.0)")
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    # Additional data for complex rewards
    extra_data = models.JSONField(default=dict, blank=True, help_text="Additional reward configuration")

    class Meta:
        ordering = ['-probability']

    def __str__(self):
        return f"{self.name} ({self.reward_type})"


class SpinWheelHistory(models.Model):
    """
    Track user spin history and enforce cooldowns
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='spin_history')
    reward = models.ForeignKey(SpinWheelReward, on_delete=models.CASCADE)

    # Spin details
    spin_timestamp = models.DateTimeField(auto_now_add=True)
    reward_claimed = models.BooleanField(default=False)
    reward_claimed_at = models.DateTimeField(null=True, blank=True)

    # For scratch cards
    scratch_card_revealed = models.BooleanField(default=False)
    scratch_card_data = models.JSONField(default=dict, blank=True)

    # Tracking
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)

    class Meta:
        ordering = ['-spin_timestamp']
        indexes = [
            models.Index(fields=['user', 'spin_timestamp']),
        ]

    def __str__(self):
        return f"{self.user.username} - {self.reward.name} at {self.spin_timestamp}"

    @classmethod
    def can_user_spin(cls, user):
        """
        Check if user can spin (once per day limit)
        """
        today = timezone.now().date()
        today_spins = cls.objects.filter(
            user=user,
            spin_timestamp__date=today
        ).count()
        return today_spins == 0

    @classmethod
    def get_user_last_spin(cls, user):
        """
        Get user's last spin time
        """
        last_spin = cls.objects.filter(user=user).first()
        return last_spin.spin_timestamp if last_spin else None

    @classmethod
    def get_next_spin_time(cls, user):
        """
        Get when user can spin next (next day at midnight)
        """
        last_spin = cls.get_user_last_spin(user)
        if not last_spin:
            return timezone.now()  # Can spin now

        # Next spin available at midnight of next day
        next_day = last_spin.date() + timedelta(days=1)
        return timezone.datetime.combine(next_day, timezone.datetime.min.time())


class ScratchCard(models.Model):
    """
    Scratch card rewards with hidden prizes
    """
    SCRATCH_TYPES = [
        ('token_reveal', 'Token Reveal'),
        ('discount_reveal', 'Discount Reveal'),
        ('mystery_box', 'Mystery Box'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    spin_history = models.OneToOneField(SpinWheelHistory, on_delete=models.CASCADE, related_name='scratch_card')

    # Scratch card details
    card_type = models.CharField(max_length=20, choices=SCRATCH_TYPES, default='token_reveal')
    hidden_reward = models.JSONField(help_text="The actual reward hidden under the scratch")
    revealed = models.BooleanField(default=False)
    revealed_at = models.DateTimeField(null=True, blank=True)

    # Visual configuration
    card_design = models.CharField(max_length=50, default='default')
    scratch_areas = models.JSONField(default=list, help_text="Areas that need to be scratched")

    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Scratch Card for {self.spin_history.user.username}"

    def reveal_card(self):
        """
        Reveal the scratch card and process rewards
        """
        if self.revealed:
            return False

        self.revealed = True
        self.revealed_at = timezone.now()
        self.save()

        # Process the hidden reward
        return self._process_hidden_reward()

    def _process_hidden_reward(self):
        """
        Process the hidden reward (tokens, discounts, etc.)
        """
        from wallet.models import Wallet, WalletTransaction

        reward_data = self.hidden_reward
        user = self.spin_history.user

        if reward_data.get('type') == 'tokens':
            # Award tokens
            wallet, _ = Wallet.objects.get_or_create(user=user)
            token_amount = reward_data.get('amount', 0)

            wallet.add_tokens(
                amount=token_amount,
                transaction_type='spin_wheel_scratch',
                description=f"Scratch card reward: {token_amount} tokens"
            )

            return {
                'success': True,
                'reward_type': 'tokens',
                'amount': token_amount,
                'message': f"You won {token_amount} tokens!"
            }

        elif reward_data.get('type') == 'discount':
            # Create discount code or add to user profile
            discount_percent = reward_data.get('amount', 0)
            return {
                'success': True,
                'reward_type': 'discount',
                'amount': discount_percent,
                'message': f"You won {discount_percent}% discount on your next purchase!"
            }

        return {
            'success': False,
            'message': 'Unknown reward type'
        }


class SpinWheelSettings(models.Model):
    """
    Global settings for the spin wheel
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Cooldown settings
    cooldown_hours = models.PositiveIntegerField(default=24, help_text="Hours between spins")

    # Wheel configuration
    wheel_segments = models.PositiveIntegerField(default=8, help_text="Number of wheel segments")
    animation_duration = models.PositiveIntegerField(default=3000, help_text="Spin animation duration in ms")

    # Reward settings
    min_token_reward = models.PositiveIntegerField(default=1)
    max_token_reward = models.PositiveIntegerField(default=50)
    scratch_card_probability = models.FloatField(default=0.2, help_text="Probability of getting scratch card")

    # Feature flags
    is_active = models.BooleanField(default=True)
    maintenance_mode = models.BooleanField(default=False)
    maintenance_message = models.TextField(blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Spin Wheel Settings"
        verbose_name_plural = "Spin Wheel Settings"

    def __str__(self):
        return f"Spin Wheel Settings (Updated: {self.updated_at})"

    @classmethod
    def get_settings(cls):
        """
        Get current spin wheel settings (singleton pattern)
        """
        settings, created = cls.objects.get_or_create(
            id='00000000-0000-0000-0000-000000000001',
            defaults={
                'cooldown_hours': 24,
                'wheel_segments': 8,
                'animation_duration': 3000,
                'min_token_reward': 1,
                'max_token_reward': 50,
                'scratch_card_probability': 0.2,
                'is_active': True,
            }
        )
        return settings
