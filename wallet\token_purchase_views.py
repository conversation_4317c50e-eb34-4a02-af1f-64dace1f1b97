"""
Token Purchase API Views
========================

API endpoints for purchasing tokens with Razorpay integration
"""

from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.conf import settings
from django.utils import timezone
from django.db import transaction
from decimal import Decimal
import logging
import uuid

from .models import TokenPack, TokenPurchase, Wallet
from orders.razorpay_utils import create_razorpay_order, verify_payment_signature
from razorpay_settings.utils import initialize_razorpay_client

logger = logging.getLogger(__name__)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def get_token_packs(request):
    """
    Get available token packs for purchase
    
    GET /api/wallet/token-packs/
    """
    try:
        token_packs = TokenPack.objects.filter(is_active=True)
        
        packs_data = []
        for pack in token_packs:
            packs_data.append({
                'id': str(pack.id),
                'name': pack.name,
                'tokens': pack.tokens,
                'price_inr': float(pack.price_inr),
                'tokens_per_rupee': pack.tokens_per_rupee,
                'is_popular': pack.tokens == 500,  # Mark 500 token pack as popular
                'savings_text': f"Best Value!" if pack.tokens == 1000 else None
            })
        
        return Response({
            'success': True,
            'token_packs': packs_data
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Error fetching token packs: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to fetch token packs'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def create_token_purchase_order(request):
    """
    Create a Razorpay order for token purchase
    
    POST /api/wallet/create-token-order/
    {
        "token_pack_id": "uuid"
    }
    """
    try:
        token_pack_id = request.data.get('token_pack_id')
        
        if not token_pack_id:
            return Response({
                'success': False,
                'error': 'Token pack ID is required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Get token pack
        try:
            token_pack = TokenPack.objects.get(id=token_pack_id, is_active=True)
        except TokenPack.DoesNotExist:
            return Response({
                'success': False,
                'error': 'Invalid token pack'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Create token purchase record
        with transaction.atomic():
            token_purchase = TokenPurchase.objects.create(
                user=request.user,
                token_pack=token_pack,
                tokens_purchased=token_pack.tokens,
                amount_paid=token_pack.price_inr,
                payment_status='pending'
            )
            
            # Create Razorpay order
            try:
                razorpay_order = create_razorpay_order(
                    order_id=token_purchase.id,
                    amount_in_inr=token_pack.price_inr
                )
                
                # Update token purchase with Razorpay order ID
                token_purchase.razorpay_order_id = razorpay_order['id']
                token_purchase.save()
                
                return Response({
                    'success': True,
                    'purchase_id': str(token_purchase.id),
                    'razorpay_order': {
                        'order_id': razorpay_order['id'],
                        'amount': razorpay_order['amount'],
                        'currency': razorpay_order['currency'],
                        'key_id': settings.RAZORPAY_KEY_ID
                    },
                    'token_pack': {
                        'name': token_pack.name,
                        'tokens': token_pack.tokens,
                        'price_inr': float(token_pack.price_inr)
                    }
                }, status=status.HTTP_201_CREATED)
                
            except Exception as e:
                logger.error(f"Error creating Razorpay order: {str(e)}")
                return Response({
                    'success': False,
                    'error': 'Failed to create payment order'
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                
    except Exception as e:
        logger.error(f"Error creating token purchase order: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to create token purchase order'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def verify_token_purchase_payment(request):
    """
    Verify token purchase payment and credit tokens
    
    POST /api/wallet/verify-token-payment/
    {
        "purchase_id": "uuid",
        "razorpay_payment_id": "pay_xxx",
        "razorpay_order_id": "order_xxx", 
        "razorpay_signature": "signature_xxx"
    }
    """
    try:
        purchase_id = request.data.get('purchase_id')
        razorpay_payment_id = request.data.get('razorpay_payment_id')
        razorpay_order_id = request.data.get('razorpay_order_id')
        razorpay_signature = request.data.get('razorpay_signature')
        
        # Validate required fields
        if not all([purchase_id, razorpay_payment_id, razorpay_order_id, razorpay_signature]):
            return Response({
                'success': False,
                'error': 'Missing required payment verification data'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Get token purchase
        try:
            token_purchase = TokenPurchase.objects.get(
                id=purchase_id,
                user=request.user,
                payment_status='pending'
            )
        except TokenPurchase.DoesNotExist:
            return Response({
                'success': False,
                'error': 'Invalid purchase or already processed'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Verify payment signature
        try:
            is_valid = verify_payment_signature(
                razorpay_order_id=razorpay_order_id,
                razorpay_payment_id=razorpay_payment_id,
                razorpay_signature=razorpay_signature
            )
            
            if not is_valid:
                token_purchase.payment_status = 'failed'
                token_purchase.notes = 'Payment signature verification failed'
                token_purchase.save()
                
                return Response({
                    'success': False,
                    'error': 'Payment verification failed'
                }, status=status.HTTP_400_BAD_REQUEST)
                
        except Exception as e:
            logger.error(f"Error verifying payment signature: {str(e)}")
            return Response({
                'success': False,
                'error': 'Payment verification failed'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        # Complete the purchase
        with transaction.atomic():
            # Update payment details
            token_purchase.razorpay_payment_id = razorpay_payment_id
            token_purchase.razorpay_signature = razorpay_signature
            
            # Complete purchase and credit tokens
            wallet = token_purchase.complete_purchase()
            
            return Response({
                'success': True,
                'message': f'Successfully purchased {token_purchase.tokens_purchased} tokens!',
                'tokens_purchased': token_purchase.tokens_purchased,
                'amount_paid': float(token_purchase.amount_paid),
                'new_balance': wallet.balance,
                'purchase_id': str(token_purchase.id)
            }, status=status.HTTP_200_OK)
            
    except Exception as e:
        logger.error(f"Error verifying token purchase payment: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to verify payment'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def get_purchase_history(request):
    """
    Get user's token purchase history
    
    GET /api/wallet/purchase-history/
    """
    try:
        purchases = TokenPurchase.objects.filter(user=request.user)
        
        purchase_data = []
        for purchase in purchases:
            purchase_data.append({
                'id': str(purchase.id),
                'token_pack_name': purchase.token_pack.name,
                'tokens_purchased': purchase.tokens_purchased,
                'amount_paid': float(purchase.amount_paid),
                'payment_status': purchase.payment_status,
                'created_at': purchase.created_at.isoformat(),
                'completed_at': purchase.completed_at.isoformat() if purchase.completed_at else None
            })
        
        return Response({
            'success': True,
            'purchases': purchase_data
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Error fetching purchase history: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to fetch purchase history'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
