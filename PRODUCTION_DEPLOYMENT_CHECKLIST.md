# 🚀 Production Deployment Checklist - Token Purchase Feature

## 📋 **PRE-DEPLOYMENT CHECKLIST**

### **✅ Backend Configuration**

#### **1. Environment Variables (Render Backend)**
Ensure these are set in your Render backend service:

```bash
# Razorpay Live Keys
RAZORPAY_KEY_ID=***********************
RAZORPAY_KEY_SECRET=y0CXtCz5Cdfet2PcH3tXetia

# Database
DATABASE_URL=postgresql://...

# Django Settings
SECRET_KEY=your-production-secret-key
DEBUG=False
ALLOWED_HOSTS=pickmetrendofficial-render.onrender.com

# Email (SendGrid)
SENDGRID_API_KEY=your-sendgrid-api-key

# Cloudinary (Media Storage)
CLOUDINARY_CLOUD_NAME=your-cloud-name
CLOUDINARY_API_KEY=your-api-key
CLOUDINARY_API_SECRET=your-api-secret
```

#### **2. Database Migrations**
```bash
# Run these commands on production
python manage.py makemigrations wallet
python manage.py migrate
python manage.py create_token_packs
```

#### **3. Token Packs Setup**
Ensure token packs are created in production:
- Starter Pack: 100 tokens for ₹10
- Popular Pack: 500 tokens for ₹45  
- Best Value Pack: 1000 tokens for ₹80

### **✅ Frontend Configuration**

#### **1. Environment Variables (Render Frontend)**
Ensure these are set in your Render frontend service:

```bash
# API Configuration
REACT_APP_API_URL=https://pickmetrendofficial-render.onrender.com
REACT_APP_ENVIRONMENT=production

# Razorpay Configuration
REACT_APP_RAZORPAY_KEY_ID=***********************
REACT_APP_PAYMENT_TEST_MODE=false

# CORS Configuration
REACT_APP_ENABLE_CORS=true

# Build Configuration
NODE_VERSION=20.11.0
CI=false
```

#### **2. Build Configuration**
- ✅ `render.yaml` updated with new environment variables
- ✅ `.env.production` configured for production
- ✅ Build command includes token purchase components

---

## 🔧 **DEPLOYMENT STEPS**

### **Step 1: Backend Deployment**

1. **Push Backend Changes**
   ```bash
   cd /path/to/backend
   git add .
   git commit -m "Add token purchase feature for production"
   git push origin main
   ```

2. **Verify Backend Deployment**
   - Check Render dashboard for successful deployment
   - Verify environment variables are set
   - Check logs for any errors

3. **Test Backend APIs**
   ```bash
   # Test token packs endpoint
   curl https://pickmetrendofficial-render.onrender.com/api/wallet/token-packs/
   
   # Should return 401 (authentication required) - this is correct
   ```

### **Step 2: Frontend Deployment**

1. **Push Frontend Changes**
   ```bash
   cd /path/to/frontend
   git add .
   git commit -m "Add token purchase feature for production"
   git push origin master  # Note: frontend uses 'master' branch
   ```

2. **Verify Frontend Deployment**
   - Check Render dashboard for successful build
   - Verify environment variables are set
   - Check build logs for any errors

3. **Test Frontend Build**
   - Visit deployed frontend URL
   - Check that token purchase page loads
   - Verify payment mode indicator shows "Live Mode"

---

## 🧪 **POST-DEPLOYMENT TESTING**

### **Step 1: Basic Functionality**

1. **Access Token Purchase Page**
   - Go to: `https://your-frontend-url.onrender.com/buy-tokens`
   - Should show "Live Mode: Real Razorpay payments enabled"
   - Token packs should load correctly

2. **Authentication Flow**
   - Login with test account
   - Navigate to wallet page
   - Click "Buy Tokens" button
   - Should redirect to token purchase page

### **Step 2: Payment Integration**

1. **Test Order Creation**
   - Select a token pack
   - Click "Proceed to Payment"
   - Should open Razorpay payment dialog
   - Check browser console for debug logs

2. **Test Payment Flow**
   - Use Razorpay test cards in live mode:
     - Success: `4111 1111 1111 1111`
     - Failure: `4000 0000 0000 0002`
   - Complete payment process
   - Verify tokens are added to wallet

### **Step 3: Error Handling**

1. **Test Network Errors**
   - Simulate network issues
   - Verify error messages are shown
   - Check that user can retry

2. **Test Payment Failures**
   - Use failure test card
   - Verify error handling
   - Check that user can try again

---

## 🔍 **MONITORING & VERIFICATION**

### **Backend Monitoring**

1. **Check Logs**
   ```bash
   # In Render dashboard, check backend logs for:
   - "Using Razorpay Key ID: rzp_live_..."
   - "Token purchase order created successfully"
   - No 500 errors in token purchase endpoints
   ```

2. **Database Verification**
   ```bash
   # Check that token purchases are being recorded
   # Admin panel: /admin/wallet/tokenpurchase/
   ```

### **Frontend Monitoring**

1. **Check Console Logs**
   ```javascript
   // Should see in browser console:
   "💳 Real payment mode - connecting to Razorpay"
   "🚀 Opening Razorpay with options: ..."
   "✅ Payment successful, verifying..."
   ```

2. **Performance Monitoring**
   - Page load times
   - API response times
   - Payment dialog loading

---

## 🚨 **TROUBLESHOOTING**

### **Common Issues**

1. **"Payment Test Mode" Shows in Production**
   - Check `REACT_APP_PAYMENT_TEST_MODE=false` in environment
   - Restart frontend service
   - Clear browser cache

2. **Razorpay Dialog Doesn't Open**
   - Check browser console for errors
   - Verify Razorpay key ID is correct
   - Check network connectivity

3. **API Errors (500/401)**
   - Verify backend environment variables
   - Check authentication tokens
   - Review backend logs

4. **Token Balance Not Updating**
   - Check payment verification endpoint
   - Verify database transactions
   - Check wallet refresh logic

### **Emergency Rollback**

If issues occur, you can quickly disable the feature:

1. **Frontend Rollback**
   ```bash
   # Set to test mode temporarily
   REACT_APP_PAYMENT_TEST_MODE=true
   ```

2. **Backend Rollback**
   ```bash
   # Disable token purchase endpoints in urls.py
   # Comment out token purchase routes
   ```

---

## 🎯 **SUCCESS CRITERIA**

### **✅ Deployment Successful When:**

1. **Frontend**
   - ✅ Token purchase page loads without errors
   - ✅ Shows "Live Mode" indicator
   - ✅ Token packs display correctly
   - ✅ Payment dialog opens successfully

2. **Backend**
   - ✅ All token purchase APIs respond correctly
   - ✅ Razorpay orders are created successfully
   - ✅ Payment verification works
   - ✅ Tokens are added to user wallets

3. **Integration**
   - ✅ End-to-end payment flow works
   - ✅ Real payments process correctly
   - ✅ Error handling works as expected
   - ✅ User experience is smooth

---

## 📞 **SUPPORT CONTACTS**

- **Razorpay Support**: <EMAIL>
- **Render Support**: <EMAIL>
- **Development Team**: [Your team contact]

---

## 🎉 **READY FOR PRODUCTION!**

Once all checklist items are completed and tested, the token purchase feature will be live and ready for real users to purchase tokens with real payments through Razorpay! 🚀💰
